{"version": "6", "dialect": "sqlite", "id": "b2192e40-f7d8-4383-99f6-6574ffce9363", "prevId": "dc1ee654-ce62-4ca5-aef8-650a05d1bfe9", "tables": {"snapshoot_table": {"name": "snapshoot_table", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "symbol": {"name": "symbol", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "indexPrice": {"name": "indexPrice", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "markPrice": {"name": "mark<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "topTraderAccountLsRatio": {"name": "topTraderAccountLsRatio", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "openInterest": {"name": "openInterest", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "oiChangePctPositive": {"name": "oiChangePctPositive", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "basisPercentNegative": {"name": "basisPercentNegative", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "signal": {"name": "signal", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users_table": {"name": "users_table", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "nickname": {"name": "nickname", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'user'"}, "deviceIds": {"name": "deviceIds", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}