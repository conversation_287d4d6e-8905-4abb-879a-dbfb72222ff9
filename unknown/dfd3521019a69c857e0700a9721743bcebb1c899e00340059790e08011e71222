/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ContentExample: typeof import('./.vitepress/theme/components/ContentExample.vue')['default']
    ContentExamples: typeof import('./.vitepress/theme/components/ContentExamples.vue')['default']
    ContentIntegrations: typeof import('./.vitepress/theme/components/ContentIntegrations.vue')['default']
    ContentPlugins: typeof import('./.vitepress/theme/components/ContentPlugins.vue')['default']
    LinkGrid: typeof import('./.vitepress/theme/components/LinkGrid.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
